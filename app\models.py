"""
Pydantic models for request/response validation
"""

from pydantic import BaseModel, Field, field_validator
from typing import List, Optional
from datetime import datetime

class TranslationRequest(BaseModel):
    """Model for single translation request"""
    text: str = Field(..., max_length=1000, description="Text to translate (max 1000 characters)")
    target_language: str = Field(..., min_length=2, max_length=5, description="Target language ISO code (e.g., 'hi', 'ta', 'kn', 'bn')")
    source_language: Optional[str] = Field(None, min_length=2, max_length=5, description="Source language ISO code (auto-detect if not provided)")

    @field_validator('text')
    @classmethod
    def text_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError('Text cannot be empty')
        return v.strip()

    @field_validator('target_language', 'source_language')
    @classmethod
    def validate_language_code(cls, v):
        if v is None:
            return v
        # Basic validation for common language codes
        valid_codes = {
            'hi', 'ta', 'kn', 'bn', 'te', 'ml', 'gu', 'pa', 'or', 'as',  # Indian languages
            'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh',  # International languages
            'ar', 'tr', 'nl', 'sv', 'da', 'no', 'fi', 'pl', 'cs', 'hu'   # More languages
        }
        if v.lower() not in valid_codes:
            raise ValueError(f'Unsupported language code: {v}')
        return v.lower()

class BulkTranslationRequest(BaseModel):
    """Model for bulk translation request"""
    texts: List[str] = Field(..., max_length=50, description="List of texts to translate (max 50 items)")
    target_language: str = Field(..., min_length=2, max_length=5, description="Target language ISO code")
    source_language: Optional[str] = Field(None, min_length=2, max_length=5, description="Source language ISO code")

    @field_validator('texts')
    @classmethod
    def validate_texts(cls, v):
        if not v:
            raise ValueError('Texts list cannot be empty')
        for i, text in enumerate(v):
            if not text.strip():
                raise ValueError(f'Text at index {i} cannot be empty')
            if len(text) > 1000:
                raise ValueError(f'Text at index {i} exceeds 1000 characters')
        return [text.strip() for text in v]

class TranslationResponse(BaseModel):
    """Model for translation response"""
    original_text: str
    translated_text: str
    source_language: str
    target_language: str
    confidence: Optional[float] = None
    timestamp: datetime

class BulkTranslationResponse(BaseModel):
    """Model for bulk translation response"""
    translations: List[TranslationResponse]
    total_count: int
    success_count: int
    timestamp: datetime

class ErrorResponse(BaseModel):
    """Model for error responses"""
    error: str
    message: str
    timestamp: datetime

class HealthResponse(BaseModel):
    """Model for health check response"""
    status: str
    timestamp: datetime
    version: str
    database_status: str
