"""
Translation API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession
from app.models import (
    TranslationRequest, 
    TranslationResponse, 
    BulkTranslationRequest, 
    BulkTranslationResponse,
    ErrorResponse
)
from app.database import get_db, log_translation
from app.services.translation_service import translation_service
from datetime import datetime
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

def get_client_info(request: Request):
    """Extract client information from request"""
    return {
        "ip_address": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent", "")[:500]  # Limit length
    }

@router.post("/translate", response_model=TranslationResponse)
async def translate_text(
    translation_request: TranslationRequest,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Translate a single text
    
    - **text**: Text to translate (max 1000 characters)
    - **target_language**: Target language ISO code (e.g., 'hi', 'ta', 'kn', 'bn')
    - **source_language**: Source language ISO code (optional, auto-detect if not provided)
    """
    try:
        # Perform translation
        result = await translation_service.translate_text(
            text=translation_request.text,
            target_language=translation_request.target_language,
            source_language=translation_request.source_language
        )
        
        # Create response
        response = TranslationResponse(
            original_text=translation_request.text,
            translated_text=result["translated_text"],
            source_language=result["source_language"],
            target_language=translation_request.target_language,
            confidence=result.get("confidence"),
            timestamp=datetime.utcnow()
        )
        
        # Log the translation
        client_info = get_client_info(request)
        await log_translation(
            session=db,
            original_text=translation_request.text,
            translated_text=result["translated_text"],
            source_language=result["source_language"],
            target_language=translation_request.target_language,
            confidence=result.get("confidence"),
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"]
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Translation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Translation service error: {str(e)}"
        )

@router.post("/translate/bulk", response_model=BulkTranslationResponse)
async def translate_bulk(
    bulk_request: BulkTranslationRequest,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Translate multiple texts in bulk
    
    - **texts**: List of texts to translate (max 50 items, each max 1000 characters)
    - **target_language**: Target language ISO code
    - **source_language**: Source language ISO code (optional)
    """
    try:
        # Perform bulk translation
        results = await translation_service.translate_bulk(
            texts=bulk_request.texts,
            target_language=bulk_request.target_language,
            source_language=bulk_request.source_language
        )
        
        # Create response objects and log successful translations
        translations = []
        success_count = 0
        client_info = get_client_info(request)
        
        for result in results:
            if result.get("success", True):
                translation_response = TranslationResponse(
                    original_text=result["original_text"],
                    translated_text=result["translated_text"],
                    source_language=result["source_language"],
                    target_language=bulk_request.target_language,
                    confidence=result.get("confidence"),
                    timestamp=datetime.utcnow()
                )
                translations.append(translation_response)
                success_count += 1
                
                # Log successful translation
                await log_translation(
                    session=db,
                    original_text=result["original_text"],
                    translated_text=result["translated_text"],
                    source_language=result["source_language"],
                    target_language=bulk_request.target_language,
                    confidence=result.get("confidence"),
                    ip_address=client_info["ip_address"],
                    user_agent=client_info["user_agent"]
                )
            else:
                # For failed translations, still include in response but don't log
                translation_response = TranslationResponse(
                    original_text=result["original_text"],
                    translated_text=result.get("translated_text", result["original_text"]),
                    source_language=result.get("source_language", "unknown"),
                    target_language=bulk_request.target_language,
                    confidence=0.0,
                    timestamp=datetime.utcnow()
                )
                translations.append(translation_response)
        
        return BulkTranslationResponse(
            translations=translations,
            total_count=len(bulk_request.texts),
            success_count=success_count,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"Bulk translation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Bulk translation service error: {str(e)}"
        )
