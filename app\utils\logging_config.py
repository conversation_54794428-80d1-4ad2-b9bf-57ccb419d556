"""
Logging configuration for the translation service
"""

import logging
import logging.handlers
import os
from datetime import datetime

def setup_logging():
    """Setup comprehensive logging configuration"""
    
    # Create logs directory if it doesn't exist
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            # Console handler
            logging.StreamHandler(),
            # File handler with rotation
            logging.handlers.RotatingFileHandler(
                filename=os.path.join(log_dir, "translation_service.log"),
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            )
        ]
    )
    
    # Configure specific loggers
    
    # Translation service logger
    translation_logger = logging.getLogger("app.services.translation_service")
    translation_logger.setLevel(logging.INFO)
    
    # API routes logger
    api_logger = logging.getLogger("app.routes")
    api_logger.setLevel(logging.INFO)
    
    # Database logger
    db_logger = logging.getLogger("app.database")
    db_logger.setLevel(logging.INFO)
    
    # Disable SQLAlchemy echo in production
    if os.getenv("ENVIRONMENT") == "production":
        logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    
    logging.info("Logging configuration completed")

class RequestLogger:
    """Utility class for logging API requests"""
    
    def __init__(self):
        self.logger = logging.getLogger("app.requests")
    
    def log_translation_request(
        self, 
        text: str, 
        target_language: str, 
        source_language: str = None,
        ip_address: str = None,
        user_agent: str = None
    ):
        """Log a translation request"""
        self.logger.info(
            f"Translation request - "
            f"Text length: {len(text)}, "
            f"Source: {source_language or 'auto'}, "
            f"Target: {target_language}, "
            f"IP: {ip_address or 'unknown'}"
        )
    
    def log_translation_response(
        self, 
        success: bool, 
        confidence: float = None,
        error: str = None
    ):
        """Log a translation response"""
        if success:
            self.logger.info(
                f"Translation successful - Confidence: {confidence or 'N/A'}"
            )
        else:
            self.logger.error(
                f"Translation failed - Error: {error or 'Unknown error'}"
            )
    
    def log_bulk_request(self, text_count: int, target_language: str):
        """Log a bulk translation request"""
        self.logger.info(
            f"Bulk translation request - "
            f"Text count: {text_count}, "
            f"Target: {target_language}"
        )

# Global request logger instance
request_logger = RequestLogger()
