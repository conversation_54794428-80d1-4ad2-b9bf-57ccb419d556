"""
Additional validation utilities
"""

import re
from typing import Set

# Comprehensive list of supported language codes
SUPPORTED_LANGUAGES: Set[str] = {
    # Indian languages
    'hi',  # Hindi
    'ta',  # Tamil
    'kn',  # Kannada
    'bn',  # Bengali
    'te',  # Telugu
    'ml',  # Malayalam
    'gu',  # Gujarati
    'pa',  # Punjabi
    'or',  # Odia
    'as',  # Assamese
    'mr',  # Marathi
    'ne',  # Nepali
    'si',  # Sinhala
    'ur',  # Urdu
    
    # International languages
    'en',  # English
    'es',  # Spanish
    'fr',  # French
    'de',  # German
    'it',  # Italian
    'pt',  # Portuguese
    'ru',  # Russian
    'ja',  # Japanese
    'ko',  # Korean
    'zh',  # Chinese (Simplified)
    'zh-cn',  # Chinese (Simplified)
    'zh-tw',  # Chinese (Traditional)
    'ar',  # Arabic
    'tr',  # Turkish
    'nl',  # Dutch
    'sv',  # Swedish
    'da',  # Danish
    'no',  # Norwegian
    'fi',  # Finnish
    'pl',  # Polish
    'cs',  # Czech
    'hu',  # Hungarian
    'ro',  # Romanian
    'bg',  # Bulgarian
    'hr',  # Croatian
    'sk',  # Slovak
    'sl',  # Slovenian
    'et',  # Estonian
    'lv',  # Latvian
    'lt',  # Lithuanian
    'el',  # Greek
    'he',  # Hebrew
    'th',  # Thai
    'vi',  # Vietnamese
    'id',  # Indonesian
    'ms',  # Malay
    'tl',  # Filipino
    'sw',  # Swahili
    'af',  # Afrikaans
}

def validate_language_code(language_code: str) -> bool:
    """
    Validate if a language code is supported
    
    Args:
        language_code: Language code to validate
        
    Returns:
        True if supported, False otherwise
    """
    if not language_code:
        return False
    
    return language_code.lower() in SUPPORTED_LANGUAGES

def validate_text_length(text: str, max_length: int = 1000) -> bool:
    """
    Validate text length
    
    Args:
        text: Text to validate
        max_length: Maximum allowed length
        
    Returns:
        True if valid, False otherwise
    """
    return len(text.strip()) <= max_length

def validate_text_content(text: str) -> bool:
    """
    Validate text content (not empty, contains meaningful characters)
    
    Args:
        text: Text to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not text or not text.strip():
        return False
    
    # Check if text contains at least some alphanumeric characters
    return bool(re.search(r'[a-zA-Z0-9\u0900-\u097F\u0980-\u09FF\u0A00-\u0A7F\u0A80-\u0AFF\u0B00-\u0B7F\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0D00-\u0D7F]', text))

def sanitize_text(text: str) -> str:
    """
    Sanitize input text
    
    Args:
        text: Text to sanitize
        
    Returns:
        Sanitized text
    """
    # Remove excessive whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove potentially harmful characters (basic sanitization)
    text = re.sub(r'[<>"\']', '', text)
    
    return text

def get_language_name(language_code: str) -> str:
    """
    Get human-readable language name from code
    
    Args:
        language_code: Language code
        
    Returns:
        Language name or the code itself if not found
    """
    language_names = {
        'hi': 'Hindi',
        'ta': 'Tamil',
        'kn': 'Kannada',
        'bn': 'Bengali',
        'te': 'Telugu',
        'ml': 'Malayalam',
        'gu': 'Gujarati',
        'pa': 'Punjabi',
        'or': 'Odia',
        'as': 'Assamese',
        'mr': 'Marathi',
        'ne': 'Nepali',
        'si': 'Sinhala',
        'ur': 'Urdu',
        'en': 'English',
        'es': 'Spanish',
        'fr': 'French',
        'de': 'German',
        'it': 'Italian',
        'pt': 'Portuguese',
        'ru': 'Russian',
        'ja': 'Japanese',
        'ko': 'Korean',
        'zh': 'Chinese',
        'ar': 'Arabic',
        'tr': 'Turkish',
        'nl': 'Dutch',
        'sv': 'Swedish',
        'da': 'Danish',
        'no': 'Norwegian',
        'fi': 'Finnish',
        'pl': 'Polish',
        'cs': 'Czech',
        'hu': 'Hungarian'
    }
    
    return language_names.get(language_code.lower(), language_code)
