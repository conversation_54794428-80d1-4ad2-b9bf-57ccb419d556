#!/usr/bin/env python3
"""
Simple test script to verify the translation service is working
"""

import requests
import json
import time

def test_health_endpoint():
    """Test health check endpoint"""
    try:
        response = requests.get("http://localhost:8000/api/v1/health")
        print(f"Health Check - Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_translation_endpoint():
    """Test translation endpoint"""
    try:
        data = {
            "text": "Hello world",
            "target_language": "hi"
        }
        response = requests.post("http://localhost:8000/api/v1/translate", json=data)
        print(f"Translation - Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Original: {result['original_text']}")
            print(f"Translated: {result['translated_text']}")
            print(f"Source: {result['source_language']} -> Target: {result['target_language']}")
            print(f"Confidence: {result['confidence']}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Translation test failed: {e}")
        return False

def test_bulk_translation():
    """Test bulk translation endpoint"""
    try:
        data = {
            "texts": ["Hello", "Good morning", "Thank you"],
            "target_language": "hi"
        }
        response = requests.post("http://localhost:8000/api/v1/translate/bulk", json=data)
        print(f"Bulk Translation - Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Total: {result['total_count']}, Success: {result['success_count']}")
            for i, translation in enumerate(result['translations']):
                print(f"  {i+1}. {translation['original_text']} -> {translation['translated_text']}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Bulk translation test failed: {e}")
        return False

def test_different_languages():
    """Test translation to different languages"""
    languages = [
        ("ta", "Tamil"),
        ("kn", "Kannada"),
        ("bn", "Bengali")
    ]
    
    for lang_code, lang_name in languages:
        try:
            data = {
                "text": "Hello",
                "target_language": lang_code
            }
            response = requests.post("http://localhost:8000/api/v1/translate", json=data)
            if response.status_code == 200:
                result = response.json()
                print(f"{lang_name} ({lang_code}): {result['translated_text']}")
            else:
                print(f"Failed to translate to {lang_name}: {response.text}")
        except Exception as e:
            print(f"Error testing {lang_name}: {e}")

def test_error_handling():
    """Test error handling"""
    print("\nTesting Error Handling:")
    
    # Test empty text
    try:
        data = {"text": "", "target_language": "hi"}
        response = requests.post("http://localhost:8000/api/v1/translate", json=data)
        print(f"Empty text - Status: {response.status_code}")
        if response.status_code == 422:
            print("✓ Empty text validation working")
        else:
            print("✗ Empty text validation failed")
    except Exception as e:
        print(f"Empty text test error: {e}")
    
    # Test invalid language
    try:
        data = {"text": "hello", "target_language": "invalid"}
        response = requests.post("http://localhost:8000/api/v1/translate", json=data)
        print(f"Invalid language - Status: {response.status_code}")
        if response.status_code == 422:
            print("✓ Invalid language validation working")
        else:
            print("✗ Invalid language validation failed")
    except Exception as e:
        print(f"Invalid language test error: {e}")

def main():
    """Run all tests"""
    print("=" * 60)
    print("Translation Microservice Test Suite")
    print("=" * 60)
    
    # Wait a moment for server to be ready
    time.sleep(1)
    
    tests = [
        ("Health Check", test_health_endpoint),
        ("Translation", test_translation_endpoint),
        ("Bulk Translation", test_bulk_translation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        result = test_func()
        results.append((test_name, result))
        print("✓ PASSED" if result else "✗ FAILED")
    
    print(f"\n--- Different Languages ---")
    test_different_languages()
    
    test_error_handling()
    
    print(f"\n{'=' * 60}")
    print("Test Summary:")
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"  {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    print("=" * 60)

if __name__ == "__main__":
    main()
