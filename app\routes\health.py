"""
Health check endpoints
"""

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.models import HealthResponse
from app.database import get_db, get_translation_stats
from datetime import datetime
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/health", response_model=HealthResponse)
async def health_check(db: AsyncSession = Depends(get_db)):
    """
    Health check endpoint
    Returns service status and basic statistics
    """
    try:
        # Check database connectivity
        stats = await get_translation_stats(db)
        database_status = stats.get("database_status", "unknown")
        
        return HealthResponse(
            status="healthy",
            timestamp=datetime.utcnow(),
            version="1.0.0",
            database_status=database_status
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            timestamp=datetime.utcnow(),
            version="1.0.0",
            database_status="error"
        )

@router.get("/stats")
async def get_stats(db: AsyncSession = Depends(get_db)):
    """
    Get translation service statistics
    """
    try:
        stats = await get_translation_stats(db)
        return {
            "status": "success",
            "data": stats,
            "timestamp": datetime.utcnow()
        }
    except Exception as e:
        logger.error(f"Failed to get stats: {e}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.utcnow()
        }
