{"tests/test_api_endpoints.py::TestTranslationAPI::test_health_endpoint": true, "tests/test_api_endpoints.py::TestTranslationAPI::test_stats_endpoint": true, "tests/test_api_endpoints.py::TestTranslationAPI::test_translate_endpoint_success": true, "tests/test_api_endpoints.py::TestTranslationAPI::test_translate_endpoint_validation_error": true, "tests/test_api_endpoints.py::TestTranslationAPI::test_translate_endpoint_with_source_language": true, "tests/test_api_endpoints.py::TestTranslationAPI::test_bulk_translate_endpoint_success": true, "tests/test_api_endpoints.py::TestTranslationAPI::test_bulk_translate_validation_error": true, "tests/test_api_endpoints.py::TestTranslationAPI::test_api_documentation_endpoints": true, "tests/test_api_endpoints.py::TestAPIErrorHandling::test_invalid_endpoint": true, "tests/test_api_endpoints.py::TestAPIErrorHandling::test_invalid_method": true, "tests/test_api_endpoints.py::TestAPIErrorHandling::test_malformed_json": true}