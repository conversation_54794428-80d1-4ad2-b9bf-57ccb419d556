"""
Test configuration and fixtures
"""

import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.database import Base, get_db
import tempfile
import os

# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test_translation.db"

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def test_engine():
    """Create test database engine"""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Cleanup
    await engine.dispose()
    # Remove test database file
    try:
        os.remove("./test_translation.db")
    except FileNotFoundError:
        pass

@pytest.fixture
async def test_session(test_engine):
    """Create test database session"""
    TestSessionLocal = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with TestSessionLocal() as session:
        yield session

@pytest.fixture
async def client(test_session):
    """Create test client with database override"""

    def override_get_db():
        return test_session

    app.dependency_overrides[get_db] = override_get_db

    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

    # Clean up
    app.dependency_overrides.clear()

@pytest.fixture
def sample_translation_request():
    """Sample translation request data"""
    return {
        "text": "Hello world",
        "target_language": "hi"
    }

@pytest.fixture
def sample_bulk_request():
    """Sample bulk translation request data"""
    return {
        "texts": ["Hello", "Good morning", "Thank you"],
        "target_language": "hi"
    }
