"""
Translation Microservice - Main Application
IIT Bombay Project Udaan Assignment

A lightweight, modular translation microservice using FastAPI
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.routes import translation, health
from app.database import init_db
from app.utils.exceptions import setup_exception_handlers
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

app = FastAPI(
    title="Translation Microservice",
    description="A lightweight translation service for Project Udaan",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup exception handlers
setup_exception_handlers(app)

# Include routers
app.include_router(health.router, prefix="/api/v1", tags=["Health"])
app.include_router(translation.router, prefix="/api/v1", tags=["Translation"])

@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    await init_db()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
