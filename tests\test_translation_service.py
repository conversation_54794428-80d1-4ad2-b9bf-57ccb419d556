"""
Unit tests for translation service
"""

import pytest
from app.services.translation_service import TranslationService

@pytest.mark.asyncio
class TestTranslationService:
    
    def setup_method(self):
        """Setup for each test method"""
        self.service = TranslationService()
    
    async def test_translate_text_mock_service(self):
        """Test translation with mock service"""
        result = await self.service.translate_text(
            text="hello",
            target_language="hi"
        )
        
        assert result["translated_text"] == "नमस्ते"
        assert result["source_language"] == "en"
        assert result["confidence"] > 0
    
    async def test_translate_text_unknown_phrase(self):
        """Test translation with unknown phrase"""
        result = await self.service.translate_text(
            text="unknown phrase",
            target_language="hi"
        )
        
        assert "[HI]" in result["translated_text"]
        assert result["source_language"] == "en"
        assert result["confidence"] < 0.85
    
    async def test_translate_bulk(self):
        """Test bulk translation"""
        texts = ["hello", "world", "unknown phrase"]
        results = await self.service.translate_bulk(
            texts=texts,
            target_language="hi"
        )
        
        assert len(results) == 3
        assert all(result["success"] for result in results)
        assert results[0]["translated_text"] == "नमस्ते"
        assert results[1]["translated_text"] == "दुनिया"
    
    async def test_partial_translation(self):
        """Test partial translation of longer text"""
        result = await self.service.translate_text(
            text="hello beautiful world",
            target_language="hi"
        )
        
        # Should translate known words
        translated = result["translated_text"]
        assert "नमस्ते" in translated  # hello
        assert "दुनिया" in translated  # world
    
    async def test_different_target_languages(self):
        """Test translation to different target languages"""
        # Test Tamil
        result_ta = await self.service.translate_text(
            text="hello",
            target_language="ta"
        )
        assert result_ta["translated_text"] == "வணக்கம்"
        
        # Test Kannada
        result_kn = await self.service.translate_text(
            text="hello",
            target_language="kn"
        )
        assert result_kn["translated_text"] == "ನಮಸ್ಕಾರ"
        
        # Test Bengali
        result_bn = await self.service.translate_text(
            text="hello",
            target_language="bn"
        )
        assert result_bn["translated_text"] == "হ্যালো"
    
    async def test_source_language_specification(self):
        """Test with explicit source language"""
        result = await self.service.translate_text(
            text="hello",
            target_language="hi",
            source_language="en"
        )
        
        assert result["source_language"] == "en"
        assert result["translated_text"] == "नमस्ते"
    
    async def test_case_insensitive_translation(self):
        """Test case insensitive translation"""
        result_lower = await self.service.translate_text(
            text="hello",
            target_language="hi"
        )
        
        result_upper = await self.service.translate_text(
            text="HELLO",
            target_language="hi"
        )
        
        result_mixed = await self.service.translate_text(
            text="Hello",
            target_language="hi"
        )
        
        # All should translate to the same result
        assert result_lower["translated_text"] == "नमस्ते"
        assert result_upper["translated_text"] == "नमस्ते"
        assert result_mixed["translated_text"] == "नमस्ते"
