"""
Database configuration and models for logging translation requests
"""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Float, Text, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# Database URL - using SQLite for simplicity
DATABASE_URL = "sqlite+aiosqlite:///./translation_logs.db"

# Create async engine
engine = create_async_engine(DATABASE_URL, echo=False)

# Create session factory
AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

Base = declarative_base()

class TranslationLog(Base):
    """SQLAlchemy model for translation logs"""
    __tablename__ = "translation_logs"

    id = Column(Integer, primary_key=True, index=True)
    original_text = Column(Text, nullable=False)
    translated_text = Column(Text, nullable=False)
    source_language = Column(String(5), nullable=False)
    target_language = Column(String(5), nullable=False)
    confidence = Column(Float, nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    ip_address = Column(String(45), nullable=True)  # Support IPv6
    user_agent = Column(String(500), nullable=True)

async def init_db():
    """Initialize database tables"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

async def get_db():
    """Dependency to get database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

async def log_translation(
    session: AsyncSession,
    original_text: str,
    translated_text: str,
    source_language: str,
    target_language: str,
    confidence: float = None,
    ip_address: str = None,
    user_agent: str = None
):
    """Log a translation request to the database"""
    log_entry = TranslationLog(
        original_text=original_text,
        translated_text=translated_text,
        source_language=source_language,
        target_language=target_language,
        confidence=confidence,
        ip_address=ip_address,
        user_agent=user_agent
    )
    session.add(log_entry)
    await session.commit()
    return log_entry

async def get_translation_stats(session: AsyncSession):
    """Get basic translation statistics"""
    from sqlalchemy import func
    
    total_translations = await session.execute(
        func.count(TranslationLog.id)
    )
    
    return {
        "total_translations": total_translations.scalar(),
        "database_status": "connected"
    }
