"""
Unit tests for validation utilities
"""

import pytest
from app.utils.validators import (
    validate_language_code,
    validate_text_length,
    validate_text_content,
    sanitize_text,
    get_language_name
)

class TestValidators:
    
    def test_validate_language_code_valid(self):
        """Test validation of valid language codes"""
        valid_codes = ['hi', 'ta', 'kn', 'bn', 'en', 'es', 'fr']
        
        for code in valid_codes:
            assert validate_language_code(code) == True
            assert validate_language_code(code.upper()) == True  # Case insensitive
    
    def test_validate_language_code_invalid(self):
        """Test validation of invalid language codes"""
        invalid_codes = ['xyz', 'invalid', '123', '', None]
        
        for code in invalid_codes:
            assert validate_language_code(code) == False
    
    def test_validate_text_length_valid(self):
        """Test validation of valid text lengths"""
        assert validate_text_length("hello") == True
        assert validate_text_length("a" * 1000) == True
        assert validate_text_length("") == True  # Empty is valid for length check
    
    def test_validate_text_length_invalid(self):
        """Test validation of invalid text lengths"""
        assert validate_text_length("a" * 1001) == False
        assert validate_text_length("a" * 1001, max_length=500) == False
    
    def test_validate_text_content_valid(self):
        """Test validation of valid text content"""
        valid_texts = [
            "hello world",
            "नमस्ते",
            "வணக்கம்",
            "123",
            "hello123",
            "text with punctuation!"
        ]
        
        for text in valid_texts:
            assert validate_text_content(text) == True
    
    def test_validate_text_content_invalid(self):
        """Test validation of invalid text content"""
        invalid_texts = [
            "",
            "   ",
            "\n\t",
            "!@#$%^&*()",  # Only special characters
            None
        ]
        
        for text in invalid_texts:
            assert validate_text_content(text) == False
    
    def test_sanitize_text(self):
        """Test text sanitization"""
        # Test whitespace normalization
        assert sanitize_text("  hello   world  ") == "hello world"
        assert sanitize_text("hello\n\nworld") == "hello world"
        
        # Test harmful character removal
        assert sanitize_text("hello<script>world") == "helloscriptworld"
        assert sanitize_text('hello"world\'test') == "helloworldtest"
        
        # Test combined
        assert sanitize_text('  hello<script>  world  ') == "helloscript world"
    
    def test_get_language_name(self):
        """Test language name retrieval"""
        assert get_language_name("hi") == "Hindi"
        assert get_language_name("ta") == "Tamil"
        assert get_language_name("en") == "English"
        assert get_language_name("unknown") == "unknown"
        assert get_language_name("HI") == "Hindi"  # Case insensitive
