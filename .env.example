# Environment Configuration Example
# Copy this file to .env and update the values

# Application Environment
ENVIRONMENT=development

# Google Cloud Translation API (Optional)
# If not provided, the service will use mock translations
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json
GOOGLE_CLOUD_PROJECT=your-project-id

# Database Configuration
DATABASE_URL=sqlite+aiosqlite:///./translation_logs.db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/translation_service.log

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Security (for production)
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1
