["tests/test_api_endpoints.py::TestAPIErrorHandling::test_invalid_endpoint", "tests/test_api_endpoints.py::TestAPIErrorHandling::test_invalid_method", "tests/test_api_endpoints.py::TestAPIErrorHandling::test_malformed_json", "tests/test_api_endpoints.py::TestTranslationAPI::test_api_documentation_endpoints", "tests/test_api_endpoints.py::TestTranslationAPI::test_bulk_translate_endpoint_success", "tests/test_api_endpoints.py::TestTranslationAPI::test_bulk_translate_validation_error", "tests/test_api_endpoints.py::TestTranslationAPI::test_health_endpoint", "tests/test_api_endpoints.py::TestTranslationAPI::test_stats_endpoint", "tests/test_api_endpoints.py::TestTranslationAPI::test_translate_endpoint_success", "tests/test_api_endpoints.py::TestTranslationAPI::test_translate_endpoint_validation_error", "tests/test_api_endpoints.py::TestTranslationAPI::test_translate_endpoint_with_source_language", "tests/test_translation_service.py::TestTranslationService::test_case_insensitive_translation", "tests/test_translation_service.py::TestTranslationService::test_different_target_languages", "tests/test_translation_service.py::TestTranslationService::test_partial_translation", "tests/test_translation_service.py::TestTranslationService::test_source_language_specification", "tests/test_translation_service.py::TestTranslationService::test_translate_bulk", "tests/test_translation_service.py::TestTranslationService::test_translate_text_mock_service", "tests/test_translation_service.py::TestTranslationService::test_translate_text_unknown_phrase", "tests/test_validators.py::TestValidators::test_get_language_name", "tests/test_validators.py::TestValidators::test_sanitize_text", "tests/test_validators.py::TestValidators::test_validate_language_code_invalid", "tests/test_validators.py::TestValidators::test_validate_language_code_valid", "tests/test_validators.py::TestValidators::test_validate_text_content_invalid", "tests/test_validators.py::TestValidators::test_validate_text_content_valid", "tests/test_validators.py::TestValidators::test_validate_text_length_invalid", "tests/test_validators.py::TestValidators::test_validate_text_length_valid"]