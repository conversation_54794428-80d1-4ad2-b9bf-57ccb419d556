"""
Custom exceptions and error handlers
"""

from fastapi import <PERSON>TTPEx<PERSON>, Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class TranslationServiceError(Exception):
    """Custom exception for translation service errors"""
    def __init__(self, message: str, error_code: str = "TRANSLATION_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class LanguageNotSupportedError(TranslationServiceError):
    """Exception for unsupported language codes"""
    def __init__(self, language_code: str):
        message = f"Language code '{language_code}' is not supported"
        super().__init__(message, "LANGUAGE_NOT_SUPPORTED")

class TextTooLongError(TranslationServiceError):
    """Exception for text that exceeds maximum length"""
    def __init__(self, length: int, max_length: int = 1000):
        message = f"Text length {length} exceeds maximum allowed length of {max_length} characters"
        super().__init__(message, "TEXT_TOO_LONG")

class EmptyTextError(TranslationServiceError):
    """Exception for empty text input"""
    def __init__(self):
        message = "Text cannot be empty"
        super().__init__(message, "EMPTY_TEXT")

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle Pydantic validation errors"""
    logger.warning(f"Validation error: {exc.errors()}")
    
    # Extract meaningful error messages
    error_details = []
    for error in exc.errors():
        field = " -> ".join(str(loc) for loc in error["loc"])
        message = error["msg"]
        error_details.append(f"{field}: {message}")
    
    return JSONResponse(
        status_code=422,
        content={
            "error": "VALIDATION_ERROR",
            "message": "Input validation failed",
            "details": error_details,
            "timestamp": datetime.utcnow().isoformat()
        }
    )

async def translation_service_exception_handler(request: Request, exc: TranslationServiceError):
    """Handle custom translation service errors"""
    logger.error(f"Translation service error: {exc.message}")
    
    return JSONResponse(
        status_code=400,
        content={
            "error": exc.error_code,
            "message": exc.message,
            "timestamp": datetime.utcnow().isoformat()
        }
    )

async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error(f"Unexpected error: {str(exc)}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "INTERNAL_SERVER_ERROR",
            "message": "An unexpected error occurred",
            "timestamp": datetime.utcnow().isoformat()
        }
    )

def setup_exception_handlers(app):
    """Setup all exception handlers for the FastAPI app"""
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(TranslationServiceError, translation_service_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
