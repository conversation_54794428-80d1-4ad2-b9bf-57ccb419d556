#!/usr/bin/env python3
"""
Development server runner for Translation Microservice
"""

import uvicorn
import os
from app.utils.logging_config import setup_logging

if __name__ == "__main__":
    # Setup logging
    setup_logging()
    
    # Get configuration from environment
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", 8000))
    workers = int(os.getenv("API_WORKERS", 1))
    
    # Run the server
    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=True,  # Enable auto-reload for development
        workers=workers if workers > 1 else None,
        log_level="info"
    )
