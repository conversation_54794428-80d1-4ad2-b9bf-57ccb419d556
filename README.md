# Translation Microservice

A lightweight, modular translation microservice built with FastAPI for IIT Bombay Project Udaan assignment.

## Features

- **RESTful API** with FastAPI
- **Google Translate API** integration with mock fallback
- **Input validation** and comprehensive error handling
- **Request logging** with SQLite database
- **Health check** endpoint
- **Bulk translation** support (up to 50 texts)
- **Comprehensive testing** with pytest
- **Docker support** for easy deployment
- **API documentation** with Swagger/OpenAPI

## Supported Languages

The service supports translation between multiple languages including:
- **Indian Languages**: Hindi (hi), Tamil (ta), Kannada (kn), Bengali (bn), Telugu (te), Malayalam (ml), Gujarati (gu), Punjabi (pa), Odia (or), Assamese (as), Marathi (mr), Urdu (ur)
- **International Languages**: English (en), Spanish (es), French (fr), German (de), Italian (it), Portuguese (pt), Russian (ru), Japanese (ja), Korean (ko), Chinese (zh), Arabic (ar), Turkish (tr), Dutch (nl), and many more

## Quick Start

### Prerequisites

- Python 3.11+
- pip or poetry
- Docker (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd translation-microservice
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables (optional)**
   ```bash
   cp .env.example .env
   # Edit .env with your Google Cloud credentials if available
   ```

5. **Run the application**
   ```bash
   uvicorn app.main:app --reload
   ```

The API will be available at `http://localhost:8000`

### Using Docker

1. **Build and run with Docker Compose**
   ```bash
   docker-compose up --build
   ```

2. **Or build and run manually**
   ```bash
   docker build -t translation-service .
   docker run -p 8000:8000 translation-service
   ```

## API Endpoints

### Health Check
- **GET** `/api/v1/health` - Service health status
- **GET** `/api/v1/stats` - Translation statistics

### Translation
- **POST** `/api/v1/translate` - Translate single text
- **POST** `/api/v1/translate/bulk` - Translate multiple texts

### Documentation
- **GET** `/docs` - Interactive API documentation (Swagger UI)
- **GET** `/redoc` - Alternative API documentation (ReDoc)

## API Usage Examples

### Single Translation

```bash
curl -X POST "http://localhost:8000/api/v1/translate" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "Hello world",
       "target_language": "hi"
     }'
```

Response:
```json
{
  "original_text": "Hello world",
  "translated_text": "नमस्ते दुनिया",
  "source_language": "en",
  "target_language": "hi",
  "confidence": 0.85,
  "timestamp": "2025-06-27T10:30:00.000Z"
}
```

### Bulk Translation

```bash
curl -X POST "http://localhost:8000/api/v1/translate/bulk" \
     -H "Content-Type: application/json" \
     -d '{
       "texts": ["Hello", "Good morning", "Thank you"],
       "target_language": "hi"
     }'
```

### Health Check

```bash
curl -X GET "http://localhost:8000/api/v1/health"
```

## Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_translation_service.py -v
```

## Project Structure

```
translation-microservice/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application
│   ├── models.py              # Pydantic models
│   ├── database.py            # Database configuration
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── health.py          # Health check endpoints
│   │   └── translation.py     # Translation endpoints
│   ├── services/
│   │   ├── __init__.py
│   │   └── translation_service.py  # Translation logic
│   └── utils/
│       ├── __init__.py
│       ├── exceptions.py      # Error handling
│       ├── validators.py      # Input validation
│       └── logging_config.py  # Logging setup
├── tests/
│   ├── __init__.py
│   ├── conftest.py           # Test configuration
│   ├── test_translation_service.py
│   ├── test_api_endpoints.py
│   └── test_validators.py
├── requirements.txt          # Python dependencies
├── Dockerfile               # Docker configuration
├── docker-compose.yml       # Docker Compose setup
├── nginx.conf              # Nginx configuration
├── .env.example            # Environment variables template
└── README.md               # This file
```

## Configuration

### Google Translate API Setup (Optional)

1. Create a Google Cloud Project
2. Enable the Google Translate API
3. Create a service account and download the JSON key
4. Set the environment variable:
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
   ```

If Google Translate API is not configured, the service will automatically use mock translations for demonstration.

### Environment Variables

- `ENVIRONMENT`: Application environment (development/production)
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to Google Cloud service account key
- `GOOGLE_CLOUD_PROJECT`: Google Cloud project ID
- `DATABASE_URL`: Database connection string
- `LOG_LEVEL`: Logging level (INFO/DEBUG/WARNING/ERROR)

## Production Deployment

### With Docker Compose (Recommended)

```bash
# Production deployment with nginx
docker-compose --profile production up -d
```

### Manual Deployment

1. Set environment to production
2. Use a production WSGI server like Gunicorn:
   ```bash
   pip install gunicorn
   gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

## Monitoring and Logging

- **Health Check**: `/api/v1/health` endpoint for monitoring
- **Statistics**: `/api/v1/stats` endpoint for usage statistics
- **Logging**: All translation requests are logged to SQLite database
- **File Logging**: Application logs are written to `logs/translation_service.log`

## Performance Considerations

- **Rate Limiting**: Implemented via nginx configuration
- **Bulk Processing**: Supports up to 50 texts per bulk request
- **Text Length**: Maximum 1000 characters per text
- **Database**: SQLite for simplicity (consider PostgreSQL for production)
- **Caching**: Consider adding Redis for frequently translated texts

## Security Features

- **Input Validation**: Comprehensive validation of all inputs
- **Error Handling**: Structured error responses without sensitive information
- **Rate Limiting**: Protection against abuse (via nginx)
- **Security Headers**: Added via nginx configuration
- **Non-root Container**: Docker container runs as non-root user

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is developed for IIT Bombay Project Udaan assignment.

## Support

For questions or issues, please contact the development team or create an issue in the repository.
