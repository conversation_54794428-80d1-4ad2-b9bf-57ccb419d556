"""
Integration tests for API endpoints
"""

import pytest
from httpx import AsyncClient

@pytest.mark.asyncio
class TestTranslationAPI:
    
    async def test_health_endpoint(self, client: AsyncClient):
        """Test health check endpoint"""
        response = await client.get("/api/v1/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
        assert "database_status" in data
    
    async def test_stats_endpoint(self, client: AsyncClient):
        """Test stats endpoint"""
        response = await client.get("/api/v1/stats")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data
        assert "timestamp" in data
    
    async def test_translate_endpoint_success(self, client: AsyncClient, sample_translation_request):
        """Test successful translation"""
        response = await client.post("/api/v1/translate", json=sample_translation_request)
        
        assert response.status_code == 200
        data = response.json()
        assert data["original_text"] == "Hello world"
        assert data["target_language"] == "hi"
        assert data["source_language"] == "en"
        assert "translated_text" in data
        assert "timestamp" in data
    
    async def test_translate_endpoint_validation_error(self, client: AsyncClient):
        """Test translation with validation errors"""
        # Empty text
        response = await client.post("/api/v1/translate", json={
            "text": "",
            "target_language": "hi"
        })
        assert response.status_code == 422
        
        # Invalid language code
        response = await client.post("/api/v1/translate", json={
            "text": "hello",
            "target_language": "invalid"
        })
        assert response.status_code == 422
        
        # Text too long
        long_text = "a" * 1001
        response = await client.post("/api/v1/translate", json={
            "text": long_text,
            "target_language": "hi"
        })
        assert response.status_code == 422
    
    async def test_translate_endpoint_with_source_language(self, client: AsyncClient):
        """Test translation with explicit source language"""
        request_data = {
            "text": "hello",
            "target_language": "hi",
            "source_language": "en"
        }
        
        response = await client.post("/api/v1/translate", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["source_language"] == "en"
    
    async def test_bulk_translate_endpoint_success(self, client: AsyncClient, sample_bulk_request):
        """Test successful bulk translation"""
        response = await client.post("/api/v1/translate/bulk", json=sample_bulk_request)
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_count"] == 3
        assert data["success_count"] >= 0
        assert len(data["translations"]) == 3
        assert "timestamp" in data
        
        # Check individual translations
        for translation in data["translations"]:
            assert "original_text" in translation
            assert "translated_text" in translation
            assert "source_language" in translation
            assert "target_language" in translation
            assert "timestamp" in translation
    
    async def test_bulk_translate_validation_error(self, client: AsyncClient):
        """Test bulk translation with validation errors"""
        # Empty texts list
        response = await client.post("/api/v1/translate/bulk", json={
            "texts": [],
            "target_language": "hi"
        })
        assert response.status_code == 422
        
        # Too many texts
        many_texts = ["hello"] * 51
        response = await client.post("/api/v1/translate/bulk", json={
            "texts": many_texts,
            "target_language": "hi"
        })
        assert response.status_code == 422
        
        # Text too long in bulk
        response = await client.post("/api/v1/translate/bulk", json={
            "texts": ["hello", "a" * 1001],
            "target_language": "hi"
        })
        assert response.status_code == 422
    
    async def test_api_documentation_endpoints(self, client: AsyncClient):
        """Test API documentation endpoints"""
        # Test OpenAPI docs
        response = await client.get("/docs")
        assert response.status_code == 200
        
        # Test ReDoc
        response = await client.get("/redoc")
        assert response.status_code == 200
        
        # Test OpenAPI JSON
        response = await client.get("/openapi.json")
        assert response.status_code == 200
        data = response.json()
        assert "info" in data
        assert "paths" in data

@pytest.mark.asyncio
class TestAPIErrorHandling:
    
    async def test_invalid_endpoint(self, client: AsyncClient):
        """Test invalid endpoint returns 404"""
        response = await client.get("/api/v1/invalid")
        assert response.status_code == 404
    
    async def test_invalid_method(self, client: AsyncClient):
        """Test invalid HTTP method"""
        response = await client.put("/api/v1/translate")
        assert response.status_code == 405
    
    async def test_malformed_json(self, client: AsyncClient):
        """Test malformed JSON request"""
        response = await client.post(
            "/api/v1/translate",
            content="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422
