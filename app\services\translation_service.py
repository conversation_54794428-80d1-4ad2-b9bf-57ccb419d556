"""
Translation Service Module
Handles Google Translate API integration with mock fallback
"""

import os
import logging
from typing import Optional, Dict, List
from datetime import datetime

logger = logging.getLogger(__name__)

class TranslationService:
    """Translation service with Google Translate API and mock fallback"""
    
    def __init__(self):
        self.use_google_api = self._check_google_credentials()
        if self.use_google_api:
            try:
                from google.cloud import translate_v2 as translate
                self.translate_client = translate.Client()
                logger.info("Google Translate API initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Google Translate API: {e}")
                self.use_google_api = False
        
        if not self.use_google_api:
            logger.info("Using mock translation service")
            self._init_mock_translations()
    
    def _check_google_credentials(self) -> bool:
        """Check if Google Cloud credentials are available"""
        return (
            os.getenv("GOOGLE_APPLICATION_CREDENTIALS") is not None or
            os.getenv("GOOGLE_CLOUD_PROJECT") is not None
        )
    
    def _init_mock_translations(self):
        """Initialize mock translation dictionary for demo purposes"""
        self.mock_translations = {
            # English to Hindi
            ("en", "hi"): {
                "hello": "नमस्ते",
                "world": "दुनिया",
                "good morning": "सुप्रभात",
                "thank you": "धन्यवाद",
                "welcome": "स्वागत",
                "how are you": "आप कैसे हैं",
                "i am fine": "मैं ठीक हूँ",
                "what is your name": "आपका नाम क्या है",
                "my name is": "मेरा नाम है",
                "nice to meet you": "आपसे मिलकर खुशी हुई"
            },
            # English to Tamil
            ("en", "ta"): {
                "hello": "வணக்கம்",
                "world": "உலகம்",
                "good morning": "காலை வணக்கம்",
                "thank you": "நன்றி",
                "welcome": "வரவேற்கிறோம்",
                "how are you": "நீங்கள் எப்படி இருக்கிறீர்கள்",
                "i am fine": "நான் நலமாக இருக்கிறேன்",
                "what is your name": "உங்கள் பெயர் என்ன",
                "my name is": "என் பெயர்",
                "nice to meet you": "உங்களை சந்தித்ததில் மகிழ்ச்சி"
            },
            # English to Kannada
            ("en", "kn"): {
                "hello": "ನಮಸ್ಕಾರ",
                "world": "ಪ್ರಪಂಚ",
                "good morning": "ಶುಭೋದಯ",
                "thank you": "ಧನ್ಯವಾದ",
                "welcome": "ಸ್ವಾಗತ",
                "how are you": "ನೀವು ಹೇಗಿದ್ದೀರಿ",
                "i am fine": "ನಾನು ಚೆನ್ನಾಗಿದ್ದೇನೆ",
                "what is your name": "ನಿಮ್ಮ ಹೆಸರು ಏನು",
                "my name is": "ನನ್ನ ಹೆಸರು",
                "nice to meet you": "ನಿಮ್ಮನ್ನು ಭೇಟಿಯಾಗಿ ಸಂತೋಷವಾಯಿತು"
            },
            # English to Bengali
            ("en", "bn"): {
                "hello": "হ্যালো",
                "world": "বিশ্ব",
                "good morning": "সুপ্রভাত",
                "thank you": "ধন্যবাদ",
                "welcome": "স্বাগতম",
                "how are you": "আপনি কেমন আছেন",
                "i am fine": "আমি ভালো আছি",
                "what is your name": "আপনার নাম কি",
                "my name is": "আমার নাম",
                "nice to meet you": "আপনার সাথে দেখা করে ভালো লাগলো"
            }
        }
    
    async def translate_text(
        self, 
        text: str, 
        target_language: str, 
        source_language: Optional[str] = None
    ) -> Dict:
        """
        Translate text using Google Translate API or mock service
        
        Args:
            text: Text to translate
            target_language: Target language code
            source_language: Source language code (auto-detect if None)
            
        Returns:
            Dictionary with translation results
        """
        try:
            if self.use_google_api:
                return await self._google_translate(text, target_language, source_language)
            else:
                return await self._mock_translate(text, target_language, source_language)
        except Exception as e:
            logger.error(f"Translation failed: {e}")
            raise Exception(f"Translation service error: {str(e)}")
    
    async def _google_translate(
        self, 
        text: str, 
        target_language: str, 
        source_language: Optional[str] = None
    ) -> Dict:
        """Translate using Google Translate API"""
        try:
            result = self.translate_client.translate(
                text,
                target_language=target_language,
                source_language=source_language
            )
            
            return {
                "translated_text": result['translatedText'],
                "source_language": result.get('detectedSourceLanguage', source_language or 'auto'),
                "confidence": 0.95  # Google API doesn't provide confidence, using default
            }
        except Exception as e:
            logger.error(f"Google Translate API error: {e}")
            # Fallback to mock translation
            return await self._mock_translate(text, target_language, source_language)
    
    async def _mock_translate(
        self, 
        text: str, 
        target_language: str, 
        source_language: Optional[str] = None
    ) -> Dict:
        """Mock translation for demo purposes"""
        # Default source language to English if not provided
        if source_language is None:
            source_language = "en"
        
        # Get translation dictionary for language pair
        translation_dict = self.mock_translations.get((source_language, target_language), {})
        
        # Try exact match first
        text_lower = text.lower().strip()
        if text_lower in translation_dict:
            translated = translation_dict[text_lower]
        else:
            # Try partial matches for longer texts
            translated = self._partial_mock_translate(text_lower, translation_dict)
            if not translated:
                # If no translation found, return formatted version
                translated = f"[{target_language.upper()}] {text}"
        
        return {
            "translated_text": translated,
            "source_language": source_language,
            "confidence": 0.85 if text_lower in translation_dict else 0.60
        }
    
    def _partial_mock_translate(self, text: str, translation_dict: Dict[str, str]) -> str:
        """Attempt partial translation for longer texts"""
        words = text.split()
        translated_words = []
        
        for word in words:
            # Remove punctuation for matching
            clean_word = word.strip('.,!?;:"()[]{}')
            if clean_word in translation_dict:
                # Preserve original punctuation
                punctuation = word[len(clean_word):]
                translated_words.append(translation_dict[clean_word] + punctuation)
            else:
                translated_words.append(word)
        
        # Only return if at least one word was translated
        if any(word in translation_dict for word in [w.strip('.,!?;:"()[]{}') for w in words]):
            return " ".join(translated_words)
        
        return ""
    
    async def translate_bulk(
        self, 
        texts: List[str], 
        target_language: str, 
        source_language: Optional[str] = None
    ) -> List[Dict]:
        """
        Translate multiple texts
        
        Args:
            texts: List of texts to translate
            target_language: Target language code
            source_language: Source language code
            
        Returns:
            List of translation results
        """
        results = []
        for text in texts:
            try:
                result = await self.translate_text(text, target_language, source_language)
                results.append({
                    "original_text": text,
                    "success": True,
                    **result
                })
            except Exception as e:
                results.append({
                    "original_text": text,
                    "success": False,
                    "error": str(e),
                    "translated_text": text,  # Fallback to original
                    "source_language": source_language or "auto",
                    "confidence": 0.0
                })
        
        return results

# Global translation service instance
translation_service = TranslationService()
